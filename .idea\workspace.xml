<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1e10888b-6f93-4c2f-8485-984214ea941a" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2yWBVQu0MQ20q4TYwNj51s5glm4" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JavaScript Debug.debug-products.html.executor": "Run",
    "JavaScript Debug.expenses.html.executor": "Run",
    "JavaScript Debug.index.html (1).executor": "Run",
    "JavaScript Debug.index.html.executor": "Run",
    "JavaScript Debug.old-index.html.executor": "Run",
    "JavaScript Debug.test_media_preview.html.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/Videos/new workspace/vortex",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="JavaScript Debug.index.html (1)">
    <configuration name="expenses.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/kkkkkkkk/Admin/expenses.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="index.html (1)" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/vortex/kkkkkkkk/index.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="index.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/kkkkkkkk/index.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="old-index.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/kkkkkkkk/old-index.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="test_media_preview.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/kkkkkkkk/test_media_preview.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JavaScript Debug.index.html (1)" />
        <item itemvalue="JavaScript Debug.index.html" />
        <item itemvalue="JavaScript Debug.expenses.html" />
        <item itemvalue="JavaScript Debug.test_media_preview.html" />
        <item itemvalue="JavaScript Debug.old-index.html" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1e10888b-6f93-4c2f-8485-984214ea941a" name="Changes" comment="" />
      <created>1749940479163</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749940479163</updated>
      <workItem from="1749940487305" duration="3302000" />
      <workItem from="1749977224548" duration="1127000" />
      <workItem from="1750019321863" duration="4953000" />
      <workItem from="1750155339292" duration="9673000" />
      <workItem from="1750178046387" duration="1350000" />
      <workItem from="1750180259449" duration="3568000" />
      <workItem from="1750235232062" duration="137000" />
      <workItem from="1750235412750" duration="3799000" />
      <workItem from="1750240038660" duration="611000" />
      <workItem from="1750247958521" duration="25469000" />
      <workItem from="1750407891324" duration="328000" />
      <workItem from="1750408454045" duration="15000" />
      <workItem from="1750461711418" duration="2265000" />
      <workItem from="1750757849746" duration="1756000" />
      <workItem from="1750765725161" duration="4135000" />
      <workItem from="1750783547729" duration="491000" />
      <workItem from="1750838041098" duration="1921000" />
      <workItem from="1751272707667" duration="1444000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>